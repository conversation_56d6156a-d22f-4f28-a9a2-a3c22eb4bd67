<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
 */
$appRole = env('APP_ROLE');

if ($appRole == 'web') {
    /*
     * The production homepage is now hosted in hubspot.
     * For local and testing environments,
     * this will redirect to the login page.
     */

    Route::get('/', 'HomeController@index');

    /*
     * Below are the old marketing routes. These are now hosted in hubspot
     */
    // Route::get('demo', 'DemoController@index');
    // Route::get('about-us', 'AuxiliaryController@about');
    // Route::get('interest-confirmation', 'AuxiliaryController@demoRedirect');
    // Route::get('success-stories', 'CaseStudyController@index');
    // Route::get('success-stories/{value}', 'CaseStudyController@detail');
    // Route::get('awards', 'Api\ThankieAwardsController@index');
    // Route::get('accessibility', 'AuxiliaryController@accessibility');
    // Route::get('careers', 'AuxiliaryController@careers');
    // Route::get('solutions', 'AuxiliaryController@solutions');
    // Route::get('features', 'AuxiliaryController@features');
    // Route::get('dynamic-personalization', 'AuxiliaryController@dynamicPersonalization');
    // Route::get('privacy', 'AuxiliaryController@privacy');
    // Route::get('terms', 'AuxiliaryController@terms');
    // Route::get('partner-terms', 'AuxiliaryController@partnerTerms');
    // Route::get('odder', 'AuxiliaryController@odder');
    // Route::get('giving', 'AuxiliaryController@giving');

    // TODO: completely remove when PLG is officially removed
//    Route::get('purchasePlan', 'PurchasePlanController@index');
//    Route::get('purchasePlan/{id}', 'PurchasePlanController@purchase');
//    Route::get('upgrade/{id}', 'PurchasePlanController@upgrade');
    Route::post('purchasePlan', 'PurchasePlanController@store');

    Route::get('freeTrial', 'FreeTrialController@index');
//    Route::get('freeTrial/extend/{id}', 'FreeTrialController@extend');
    Route::post('freeTrial', 'FreeTrialController@store');
    Route::post('freeTrial/extend', 'FreeTrialController@storeExtend');
    Route::post('freeTrial/leftPlanStep', 'FreeTrialController@leftPlanStep');
    Route::post('freeTrial/leftPaymentStep', 'FreeTrialController@leftPaymentStep');

    Route::get('karen', 'Api\KarenController@index');
    Route::get('nais', 'AuxiliaryController@nais');
    Route::get('odder_sow', 'AuxiliaryController@odderSow');

    Route::get('video/personal/{id}', 'PersonalVideoController@index');
    Route::get('video/signal/{id}', 'SignalPersonalVideoController@index');

    Route::get('video/preview/{id}', 'VideoController@preview');
    Route::get('video/preview/{id}/{purl}', 'VideoController@preview');
    Route::get('video/envelope/{slug}', 'VideoController@envelope');
    Route::get('video/landing', 'VideoController@landing');
    Route::get('video/replyRedirect/{id}', 'VideoController@replyRedirect');
    Route::get('video/replyConfirm/{id}', 'VideoController@replyConfirm');
    Route::get('video/example', 'VideoController@example');
    Route::get('video/request/{id}', 'VideoController@videoRequest');
    Route::get('video/{id}', 'VideoController@index');
    Route::get('video/{id}/generic', 'VideoController@index');
    Route::get('video/{id}/{purl}', 'VideoController@index');

    Route::get('embed/recorder', 'EmbedController@recorder');
    Route::get('embed/landing', 'VideoController@landing');
    Route::get('embed/{id}', 'VideoController@index');
    Route::get('embed/{id}/{purl}', 'VideoController@index');

    Route::get('auth/magicLink', 'Auth\LoginController@magicLinkLogin');

    Route::get('download/csv', 'DownloadController@csv');
    Route::get('unsubscribe', 'UnsubscribeController@index');
    Route::get('unsubscribe-marketing', 'UnsubscribeController@marketing');
    Route::post('resubscribe', 'UnsubscribeController@resubscribe');
    Route::post('resubscribe-marketing', 'UnsubscribeController@marketingResubscribe');
    Route::get('unsubscribe/delete', 'UnsubscribeController@delete');
    Route::post('unsubscribe/delete', 'UnsubscribeController@deleteStore');
    Route::get('unsubscribe/delete/confirm/{id}', 'UnsubscribeController@deleteConfirm');
    Route::get('verify/{uuid}', 'VerifyController@index');
    Route::get('no-js', 'ErrorJS@index');
    Route::get('tracking', 'TrackingController@index');
    Route::get('dns/{id}', 'DnsController@index');
    Route::get('quickbooks', 'Api\QuickbooksController@index');
    Route::get('sso/{slug}/{uuid}', 'SsoController@index');

    // Route needed in case client refreshes in browser and changes POST to GET request
    Route::get('auth/verify', 'AuxiliaryController@abortNotFound');
    Route::get('auth/{ssoSlug}', 'Auth\LoginController@redirectToSso');
    Route::post('auth/saml2/callback', 'Auth\LoginController@handleSamlCallback');

    Route::post('auth/verify', 'Auth\LoginController@postVerify');

    Auth::routes();
    Route::get('login_ca', 'Auth\LoginController@showLoginForm');
    Route::post('login_ca', 'Auth\\LoginController@login');

    // Route needed for Forumbee SSO
    Route::get('logout', 'Auth\LoginController@logout')->name('getLogout');
    Route::get('user/login', 'Auth\LoginController@oldLogin');

    Route::get('admin', 'Admin\AdminController@index');
    Route::get('admin_ca', 'Admin\AdminController@index');
    Route::get('adminFeatureUsage', 'Admin\FeatureUsageController@index');
    Route::get('adminCommissions', 'Admin\CommissionController@index');

    Route::get('community', 'ForumbeeApiController@index');
    Route::get('academy', 'ThinkificApiController@index');

    Route::group(['prefix' => 'api-v1'], function () {
        Route::get('auth', 'PublicApi\v1\AuthController@index');
        Route::post('auth', 'PublicApi\v1\AuthController@store');

        Route::get('projects', 'PublicApi\v1\ProjectsController@index');
        Route::get('metrics', 'PublicApi\v1\MetricsController@index');

        Route::get('guest', 'PublicApi\v1\GuestController@index');
        Route::post('guest', 'PublicApi\v1\GuestController@store');
        Route::delete('guest/{id}', 'PublicApi\v1\GuestController@destroy');
        Route::post('guest/list', 'PublicApi\v1\GuestController@addToList');

        Route::get('video', 'PublicApi\v1\VideoController@index');
        Route::get('video/reply', 'PublicApi\v1\VideoController@getReplies');
        Route::post('video', 'PublicApi\v1\VideoController@store');
        Route::post('video/save', 'PublicApi\v1\VideoController@save');
        Route::post('video/attach', 'PublicApi\v1\VideoController@attach');
        Route::delete('video/{id}', 'PublicApi\v1\VideoController@destroy');
        Route::get('video/watched', 'PublicApi\v1\VideoController@watched');

        Route::get('send', 'PublicApi\v1\SendController@index');
        Route::get('send/readyToSend', 'PublicApi\v1\SendController@getReadyToSend');
        Route::get('send/scheduled', 'PublicApi\v1\SendController@getScheduled');
        Route::get('send/sending', 'PublicApi\v1\SendController@getSending');
        Route::get('send/delivered', 'PublicApi\v1\SendController@getDelivered');
        Route::get('send/bounced', 'PublicApi\v1\SendController@getBounced');
        Route::get('send/all', 'PublicApi\v1\SendController@getAllSent');
        Route::post('send', 'PublicApi\v1\SendController@store');
        Route::post('send/cancel', 'PublicApi\v1\SendController@cancel');

        Route::get('auth/info', 'PublicApi\v1\BusinessController@authInfo');
        Route::post('business/recipientFields', 'PublicApi\v1\BusinessController@getRecipientFields');
        Route::get('business/lists', 'PublicApi\v1\BusinessController@getLists');

        Route::post('subscribe/hook', 'PublicApi\v1\SubscriptionController@subscribe');
        Route::post('unsubscribe/hook', 'PublicApi\v1\SubscriptionController@unsubscribe');
    });

    Route::group(['prefix' => 'account'], function () {
        Route::get('/', 'Account\AccountController@index')->name('account');
        Route::get('welcome', 'Account\AccountController@index');
        Route::get('campaign', 'Account\AccountController@campaign'); // TODO: handle this redirect better?
        Route::get('campaigns', 'Account\AccountController@projects')->name('campaigns');

        Route::get('portal/{id}', 'Account\AccountController@switchPortal');
        Route::get('contacts', 'Account\AccountController@contacts');
        Route::get('metrics', 'Account\AccountController@metrics');
        Route::get('metrics/{dashboard}', 'Account\AccountController@metrics');
        Route::get('metrics/{dashboard}/{id}', 'Account\AccountController@metrics');
        Route::get('library', 'Account\AccountController@library');
        Route::get('library/{id}', 'Account\AccountController@library')->name('libraryVideo');
        Route::get('personal-video', 'Account\AccountController@personalVideo');
        Route::get('lists', 'Account\AccountController@guestLists');
        Route::get('lists/{id}', 'Account\AccountController@guestList');
        Route::get('manage-users', 'Account\AccountController@manageUsersRedirect');
        Route::get('my-account', 'Account\AccountController@myAccountRedirect');
        Route::get('settings', 'Account\AccountController@settingsRedirect');
        Route::get('settings/my-profile', 'Account\AccountController@settingsMyProfile');
        Route::get('settings/my-subscription', 'Account\AccountController@settingsMySubscription');
        Route::get('settings/my-portal', 'Account\AccountController@settingsMyPortal');
        Route::get('settings/security', 'Account\AccountController@settingsSecurity');
        Route::get('settings/manage-users', 'Account\AccountController@settingsManageUsers');
        Route::get('settings/accessibility', 'Account\AccountController@settingsAccessibility');
        Route::get('settings/notifications', 'Account\AccountController@settingsNotifications');
        Route::get('settings/integrations-apis', 'Account\AccountController@settingsIntegrationsAPIs');

        Route::get('print', 'Account\AccountController@printView');
        Route::get('home', 'Account\AccountController@home')->name('home');

        Route::get('metricsExport/{id}', 'Account\MetricsExportController@download');

        // Route::get('credits', 'Account\AccountController@credits');

        Route::get('campaign/{id}', 'Account\ProjectController@index');
        Route::get('campaign/{id}/setup', 'Account\ProjectController@setup')->name('project.setup');
        Route::get('campaign/{id}/recipients', 'Account\ProjectController@recipients')->name('project.recipients');
        Route::get('campaign/{id}/page', 'Account\ProjectController@page')->name('project.page');
        Route::get('campaign/{id}/videos', 'Account\ProjectController@videosRedirect')->name('project.videos');
        Route::get('campaign/{id}/videos/intro', 'Account\ProjectController@intro')->name('project.videos.intro');
        Route::get('campaign/{id}/videos/recordings', 'Account\ProjectController@videos')->name('project.videos.recordings');
        Route::get('campaign/{id}/videos/add-on', 'Account\ProjectController@videos')->name('project.videos.add-ons');
        Route::get('campaign/{id}/videos/outro', 'Account\ProjectController@outro')->name('project.videos.outro');
        Route::get('campaign/{id}/videos/overlay', 'Account\ProjectController@videoOverlay')->name('project.videos.overlay');
        Route::get('campaign/{id}/send', 'Account\ProjectController@sends')->name('project.send');
        Route::get('campaign/{id}/replies', 'Account\ProjectController@replies')->name('project.replies');
    });

    Route::group(['prefix' => 'builder'], function () {
        // envelope builder
        Route::get('envelopes/availableCount', 'Api\Builder\AssetBuilderController@availableCount');
        Route::post('envelopes', 'Api\Builder\AssetBuilderController@store');
        Route::put('envelopes', 'Api\Builder\AssetBuilderController@update');

        Route::get('backgroundImages', 'Api\Builder\AssetBuilderController@getBackgroundImages');
        Route::post('backgroundImage', 'Api\Builder\AssetBuilderController@createBackgroundImage');
        Route::post('backgroundImage/rename', 'Api\Builder\AssetBuilderController@renameBackgroundImage');
        Route::delete('backgroundImage/{uuid}/{id}', 'Api\Builder\AssetBuilderController@deleteBackgroundImage');

        Route::get('landingPage', 'Api\Builder\AssetBuilderController@getLandingPage');
        Route::get('landingPages', 'Api\Builder\AssetBuilderController@getLandingPages');
        Route::get('landingPageStats', 'Api\Builder\AssetBuilderController@landingPageStats');
        Route::post('landingPage', 'Api\Builder\AssetBuilderController@createLandingPage');
        Route::post('updateLandingPage', 'Api\Builder\AssetBuilderController@updateLandingPage');

        Route::post('addLogEvent', 'Api\LogEventsController@addEvent');
    });

    // TEST VIEW ROUTES
    Route::get('tests/{testType}', 'TestController@index');
}

if ($appRole == 'media') {
    Route::get('embed/recorder', 'EmbedController@recorder');
    Route::get('embed/landing', 'VideoController@landing');
    Route::get('embed/preview/{id}', 'VideoController@preview');
    Route::get('embed/{id}', 'VideoController@index');

    Route::get('account/metricsExport/{id}', 'Account\MetricsExportController@download');
}
