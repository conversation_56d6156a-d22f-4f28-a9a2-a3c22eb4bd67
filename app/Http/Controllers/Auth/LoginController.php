<?php

namespace App\Http\Controllers\Auth;

use App\AdminSetting;
use App\Business;
use App\Http\Controllers\Controller;
use App\Role;
use App\Services\ForumbeeService;
use App\Services\SendEmailService;
use App\Services\SsoRedirectService;
use App\Services\ThinkificService;
use App\User;
use Auth;
use Carbon\Carbon;
use Config;
use Cookie;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Log;
use Session;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
     */

    use AuthenticatesUsers;

    // This array is needed for other API's that utilize a non-business subdomain (such as Forumbee & Thinkific)
    protected $sub_domains = ['community', 'academy'];

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->loginPath = '/login';

        $this->middleware('guest', ['except' => ['logout', 'login', 'showLoginForm', 'postVerify', 'redirectToSso', 'handleSamlCallback']]);
        $this->middleware('business', ['except' => ['login', 'showLoginForm', 'logout', 'postVerify', 'redirectToSso', 'handleSamlCallback']]);
    }

    /**
     * Show the application login form.
     *
     * @return \Illuminate\Http\Response
     */
    public function showLoginForm(Request $request)
    {
        if ($request->is('login_ca')) {
            $this->loginPath = '/login_ca';
        }
        if ($request->has('redirect')) {
            $intended = Session::put('url.intended', $request->input('redirect'));
        }

        // Store intended forumbee/thinkific url in session
        if ($request->query('url') && strpos($request->query('url'), 'community.thankview')) {
            Session::put('url.community', $request->query('url'));
        } elseif ($request->query('url') && strpos($request->query('url'), 'academy.thankview')) {
            Session::put('url.academy', $request->query('url'));
        }

        if ($this->guard()->check() && $this->guard()->user()->god) {
            $this->guard()->logout();
        }

        $params = [];
        $year = Carbon::now()->year;
        $params['year'] = $year;

        $params['content'] = config('auth.content');

        // grabbing content from the backend for
        // a login illustration and content
        try {
            $setting = AdminSetting::where('key', 'login-settings')->first();
            $content = $setting ? json_decode($setting->value) : null;

            if (!empty($content)) {
                $content = json_decode(json_encode($content), true);

                foreach ($content as $index => $c) {
                    if ($c && $c != '') {
                        $params['content'][$index] = $c;
                    }
                }
            }
        } catch (\Exception $e) {
            \Bugsnag::leaveBreadcrumb('Error while trying while trying to set up the contents for the login page.');
            \Bugsnag::notifyException($e);
        }

        // display the business logo & sso option if it's the login
        // of a business portal
        if (env('APP_HOST') != $request->header('host')) {
            $slug = explode('.', $request->header('host'))[0];
            $business = Business::whereSlug($slug)->first();
            if (!$business && !in_array($slug, $this->sub_domains)) {
                // aborting because the slug provided is not of a valid business
                abort(401);
            }
            $params['businessLogo'] = $business ? $business->logo : null;
            $params['sso_config'] = $business ? $business->ssoConfig()->join('sso_integrations', 'sso_integrations.id', '=', 'businesses_sso.sso_id')->get() : null;
            $params['js_params'] = ['sso_config' => $params['sso_config']];
        }

        return view('auth.login', $params);
    }

    /**
     * Handle a login request to the application.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {
        if ($request->is('login_ca')) {
            $this->loginPath = '/login_ca';
        }
        $this->validateLogin($request);
        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if ($this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        // get credentials from the request
        $credentials = $this->credentials($request);

        // fetch the user from the db that's attempting to login
        $attemptedUser = User::whereEmail($request->input('email'))->first();
        if (!$attemptedUser && env('APP_ENV') === 'production') {
            Config::set('database.default', 'mysql_ca');
            $attemptedUser = User::whereEmail($request->input('email'))->first();
        }

        // A user without a business id is a personal account, can't have SSO
        // If their business has an SSO config, they went to thankview.com without a slug and tried to log in
        $isGenericLoginPage = (env('APP_URL') . '/login') == url()->current();
        if (($attemptedUser && $attemptedUser->business_id && $attemptedUser->business->ssoConfig()->count()) && $isGenericLoginPage) {
            // Reject the login, they are required to use SSO if SSO is enabled
            $businessUrl = 'https://' . $attemptedUser->business->slug . '.' . env('APP_HOST', 'thankview.com');
            throw ValidationException::withMessages([
                'email' => ['Your portal has SSO enabled, please log in <a class="font-blue-48" href="' . $businessUrl . '">here</a>.'],
            ]);
        }

        // check if user has 2fa turned on
        if ($attemptedUser && !$attemptedUser->settings->two_way_authentication_confirmed) {
            // attempt to login user
            if (Auth::attempt($credentials)) {
                $this->clearLoginAttempts($request);
                return $this->redirectLoggedInUser($request);
            }
        } else {
            // validate credentials without logging in the user
            if ($this->guard()->validate($credentials)) {
                // check if there's an existing cookie with a 2fa token
                $existingToken = $request->cookie('two_factor_auth_token');

                // check if the token is still valid, if it is,
                // log the user in
                if ($existingToken && $attemptedUser->userTokens()->whereToken($existingToken)->count()) {
                    // log user in
                    $this->guard()->login($attemptedUser);

                    // clear throttles
                    $this->clearLoginAttempts($request);

                    return $this->redirectLoggedInUser($request);
                } else {
                    // send 2fa code via text
                    $attemptedUser->settings->sendTwoWayAuthCode($attemptedUser->phone);

                    $slug = explode('.', $request->header('host'))[0];

                    // get the business logo to display on the 2fa page
                    $businessLogo = null;
                    if (env('APP_HOST') != $request->header('host')) {
                        $business = Business::whereSlug($slug)->first();
                        $businessLogo = $business ? $business->logo : null;
                    } else {
                        $businessLogo = $attemptedUser->business_id ? $attemptedUser->business->logo : null;
                    }

                    $params = [
                        'id' => $attemptedUser->id,
                        'businessLogo' => $businessLogo,
                    ];

                    // return the 2fa page
                    return view('auth.verify', $params);
                }
            }
        }

        // login has failed so we increment throttle attempts
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }

    public function postVerify(Request $request)
    {
        $refererPath = parse_url($request->server('HTTP_REFERER'), PHP_URL_PATH);
        if ($request->is('login_ca') || $refererPath === '/login_ca') {
            $this->loginPath = '/login_ca';
        }
        $user = User::find($request->input('_id'));

        $maxAttempts = 5;

        // check max attempts for verifying 2fa code
        if ($user->settings->two_way_authentication_confirmed && $maxAttempts > $user->settings->two_way_authentication_attempts) {
            $token = $user->settings->verifyTwoWayAuthCode($request->input('code'));
        } else {
            $token = null;
        }

        // incorrect token, show the 2fa form again
        if (!$token) {
            if ($user->settings->two_way_authentication_attempts >= $maxAttempts) {
                $message = 'Please contact your ThankView sales rep to access your account.';
            } else {
                $message = 'The code is incorrect. You have ' . ($maxAttempts - $user->settings->two_way_authentication_attempts) . ' more attempts before your account is locked.';
            }

            $params = [
                'id' => $user->id,
                'businessLogo' => $user->business_id ? $user->business->logo : null,
            ];

            return view('auth.verify', $params)
                ->withErrors([
                    'code' => $message,
                ]);
        }

        // we're successful so let's log the user in
        $this->guard()->login($user);

        // clear throttle attempts
        $this->clearLoginAttempts($request);

        // save 2fa cookie for all domains
        $cookieDomain = '.' . env('APP_HOST');
        Cookie::queue('two_factor_auth_token', $token, 24 * 60 * 7, null, $cookieDomain);

        return $this->redirectLoggedInUser($request);
    }

    protected function redirectLoggedInUser($request)
    {
        $user = $this->guard()->user();

        // a way to block some users
        if ($user->block) {
            $this->guard()->logout();
            Log::info('user is blocked.', ['user_id' => $user->id, 'user_email' => $user->email]);
            abort(500);
        }

        if (env('APP_HOST') === $request->header('host') || in_array(explode('.', $request->header('host'))[0], $this->sub_domains)) {
            $business = $user->business ?: $user->businesses()->first();
        } else {
            $business = Business::whereSlug(explode('.', $request->header('host'))[0])->first();
        }
        Session::forget('sso');

        // restrict portal logins to certain ip addresses
        // this is a solution for Harvard before we build SSO
        $bizIps = Config::get('auth.bizIps');
        if (!$user->god && $business && isset($bizIps[$business->slug])) {
            $allowedIpRanges = $bizIps[$business->slug];
            $ipAddress = ip2long($request->ip());
            $allowed = false;
            foreach ($allowedIpRanges as $range) {
                if (ip2long($range['low']) <= $ipAddress && $ipAddress <= ip2long($range['high'])) {
                    $allowed = true;
                }
            }
            if (!$allowed) {
                $this->guard()->logout();

                return redirect($this->loginPath)
                    ->withErrors([
                        'business' => 'Your IP address does not have access to ' . $business->name . '.',
                    ]);
            }
        }

        // check that the business is active and the user is logged
        // into a business they are part of
        if (!$user->god && $business && ($business->suspended || $business->isPaywallActive() || !$user->businesses()->find($business->id))) {
            // log the user out as they're in the wrong portal
            // or the portal is suspended / inactive
            $this->guard()->logout();

            $errorMessage = 'This user does not have access to ' . $business->name . '.';

            if ($business->isPaywallActive()) {
                $errorMessage = 'This account does not have an active subscription. Please visit <a href="https://' . $business->slug . '.thankview.com">' . $business->slug . '.thankview.com</a> to activate.';
            }

            return redirect($this->loginPath)
                ->withErrors([
                    'business' => $errorMessage,
                ]);
        } else {
            // get the url they were intending to go to from the session facade
            $intended = Session::pull('url.intended');
            $forumbeeRedirect = Session::pull('url.community');
            $thinkificRedirect = Session::pull('url.academy');

            // don't allow users to visit /admin
            if (strpos($intended, '/admin') && !$user->god) {
                Session::forget('url.intended');
                $intended = null;
            }

            // set user to the business they're logging into
            if ($business && $user->business_id != $business->id) {
                $user->business_id = $business->id;
                $user->save();
            }

            if ($business && $user->god) {
                $user->attachBusiness($business);
                $user->attachRole($business, Role::whereSlug('admin')->first());
            }

            // direct user to their intended url
            // only for account or admin pages
            // to avoid a weird redirect back the login page
            if ($intended && strpos($intended, '/account') !== false) {
                $redirectTo = $intended;
                if ($business) {
                    $urlPath = parse_url($redirectTo, PHP_URL_PATH);
                    $redirectTo = $business->getPortalUrl() . ($urlPath ? $urlPath : '');
                }
                return redirect($redirectTo);
            }

            if ($intended && strpos($intended, '/admin') !== false) {
                return redirect($intended);
            }

            if ($forumbeeRedirect) {
                $forumbeeService = app()->make(ForumbeeService::class);
                return redirect($forumbeeService->getUrl($forumbeeRedirect));
            } elseif ($thinkificRedirect) {
                $thinkificService = app()->make(ThinkificService::class);
                return redirect($thinkificService->getUrl($thinkificRedirect));
            }


            // sync with intercom on login
            if (!$user->god) {
                $user->intercomUpdateData();
            }

            // redirect the user to welcome page
            Session::forget('url.intended');
            $redirectTo = $this->redirectPath();

            if ($business && env('APP_ENV') === 'production') {
                $urlPath = parse_url($redirectTo, PHP_URL_PATH);
                $redirectTo = $business->getPortalUrl() . ($urlPath ? $urlPath : '');
            }
            return redirect($redirectTo);
        }
    }

    /**
     * Log the user out of the application.
     *
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        if ($request->input('stay')) {
            $redirect = '/login';
        } else {
            $redirect = property_exists($this, 'redirectAfterLogout') ? $this->redirectAfterLogout : '/';
        }
        $this->guard()->logout();

        return redirect($redirect);
    }

    /**
     * Get the post register / login redirect path.
     *
     * @return string
     */
    public function redirectPath()
    {
        if ($this->guard()->check()) {
            $user = $this->guard()->user();
            $this->redirectTo = '/account/home';
        } else {
            $this->redirectTo = '/';
        }

        return $this->redirectTo;
    }

    /**
     * Redirect the user to the identity provider's authentication page.
     *
     * @return \Illuminate\Http\Response
     */
    public function redirectToSso(Request $request, $ssoSlug)
    {
        $bizSlug = explode('.', $request->header('host'))[0];
        $biz = Business::whereSlug($bizSlug)->first();

        $ssoRedirectService = app()->makeWith(SsoRedirectService::class, ['biz' => $biz, 'slug' => $ssoSlug]);
        return $ssoRedirectService->redirect();
    }

    /**
     * Obtain the user information from the identity provider.
     *
     * @return \Illuminate\Http\Response
     */
    public function handleSamlCallback(Request $request)
    {
        $bizSlug = explode('.', $request->header('host'))[0];
        $biz = Business::whereSlug($bizSlug)->first();
        $slug = $biz->ssoConfig()->join('sso_integrations', 'sso_integrations.id', '=', 'businesses_sso.sso_id')->where('sso_integrations.slug', 'like', 'saml2-%')->first();

        $ssoRedirectService = app()->makeWith(SsoRedirectService::class, ['biz' => $biz, 'slug' => $slug, 'loginPath' => $this->loginPath, 'redirectPath' => $this->redirectPath()]);
        return $ssoRedirectService->samlCallback($bizSlug);
    }

    public function oldLogin()
    {
        return redirect('login');
    }

    public function magicLinkLogin(Request $request)
    {
        $token = $request->input('token');

        $user = User::whereMagicLinkToken($token)->first();

        if (!$user || Carbon::parse($user->magic_link_token_date)->isBefore(Carbon::now()->subMinutes(30))) {
            // if link is over 30 minutes old, redirect user to error page
            abort(401);
        }

        $this->guard()->login($user);

        $user->magic_link_token = null;
        $user->save();

        return $this->redirectLoggedInUser($request);
    }
}
