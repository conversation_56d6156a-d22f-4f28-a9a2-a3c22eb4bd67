/* eslint-disable import/no-extraneous-dependencies */
const mix = require('laravel-mix');
const path = require('path');
const packageJson = require('./package.json');
const webpack = require('webpack');

require("laravel-mix-purgecss");
require("laravel-mix-polyfill");

const runtimeScripts = [
  "resources/js/global/plugins/shim.js",
  "resources/js/global/plugins/plugins.js",
  process.env.NODE_ENV === "development"
    ? "node_modules/angular/angular.js"
    : "node_modules/angular/angular.min.js", // not using minified version so we get useful dev-mode stuff
  process.env.NODE_ENV === "development"
    ? "node_modules/angular-touch/angular-touch.js"
    : "node_modules/angular-touch/angular-touch.min.js", // not using minified version so we get useful dev-mode
  "resources/js/global/plugins/ui-bootstrap-custom-0.13.4.js",
  "resources/js/global/plugins/transition-end.js",
  "node_modules/a11y-dialog/dist/a11y-dialog.min.js",
  "node_modules/dayjs/dayjs.min.js",
  "node_modules/dayjs/plugin/customParseFormat.js",
  "node_modules/focus-visible/dist/focus-visible.min.js",
  // include vue and ngVue
  process.env.NODE_ENV === "development"
    ? "node_modules/vue/dist/vue.js"
    : "node_modules/vue/dist/vue.min.js", // not using minified version so we get useful dev-mode stuff
  "node_modules/ngVue/build/index.js",
  "node_modules/fix-webm-duration/fix-webm-duration.js",
  "node_modules/url-search-params-polyfill/index.js",
];

const preLoginScripts = [
  "resources/js/app.js",

  "resources/js/global/ajax.js",
  "resources/js/global/compile-buttons.js",
  "resources/js/global/dates.js",
  "resources/js/global/demo.js",
  // 'resources/js/global/demo-link-service.js',
  "resources/js/global/easing.js",
  "resources/js/global/flash-warning.js",
  "resources/js/global/fit-text.js",
  "resources/js/global/http-progress.js",
  "resources/js/global/hubspot-service.js",
  "resources/js/global/intercom-service.js",
  "resources/js/global/lazy-loader.js",
  "resources/js/global/log-event-service.js",
  "resources/js/global/media-server-service.js",
  "resources/js/global/overlay-demo.js",
  "resources/js/global/overlay-see-example.js",
  "resources/js/global/overlay-message.js",
  "resources/js/global/overlay.js",
  "resources/js/global/purchase-flow.js",
  "resources/js/global/smooth-scroll-service.js",
  "resources/js/global/socket-service.js",
  "resources/js/global/stripe-form.js",
  "resources/js/global/stripe-service.js",
  "resources/js/global/transition.js",
  "resources/js/global/vid-player.js",
  "resources/js/global/video-js.js",
  "resources/js/global/package-service.js",

  "resources/js/global/nav/footer.js",
  "resources/js/global/nav/header.js",

  "resources/js/free-trial/free-trial.js",
  "resources/js/free-trial/free-trial-setup.js",
  "resources/js/free-trial/free-trial-setup-form.js",

  "resources/js/auth/login.js",
  "resources/js/auth/register.js",
  "resources/js/auth/sign-up.js",
  "resources/js/auth/auth-form.js",

  "resources/js/pricing/pricing.js",
  "resources/js/pricing/pricing-service.js",
  "resources/js/pricing/overlay-paywall.js",

  "resources/js/recorder/recorder.js",
  "resources/js/recorder/recorder-service.js",
  "resources/js/recorder/recorder-webcam-service.js",
  "resources/js/recorder/recorder-upload-service.js",

  "resources/js/recordings-preview/overlay-recordings-preview.js",

  "resources/js/video/video.js",
  "resources/js/video/video-player.js",
  "resources/js/video/video-reports.js",
  "resources/js/video/overlay-reply.js",

  "resources/js/video-request/video-request.js",
  "resources/js/video-request/video-request-confirmation.js",
  "resources/js/video-request/overlay-request-submit.js",

  "resources/js/embed/embed-recorder.js",

  "resources/js/odder/odder-example.js",

  "resources/js/angular-react-components.js",
];

/**
 * JS files required when a user is logged in
 */
const postLoginScripts = [
  "resources/js/global/plugins/nodep-date-input-polyfill.dist.js",
  "resources/js/global/plugins/webvtt-parser.js",

  "resources/js/global/plugins/angular.audio.js",
  "resources/js/global/compare-to.js",
  "resources/js/global/nav/header-account-layout.js",
  "resources/js/global/workflow-validation-service.js",
  "resources/js/global/upload-spreadsheet-service.js",

  "resources/js/account/account.js",
  "resources/js/account/asset-builder-container.js",
  "resources/js/account/asset-builder-widget.js",
  "resources/js/account/businesses-service.js",
  "resources/js/account/clear-input.js",
  "resources/js/account/contact-data-service.js",
  "resources/js/account/convert-links-filter.js",
  "resources/js/global/intercom-service.js",
  "resources/js/account/merge-field-filter.js",
  "resources/js/account/account-alerts.js",
  "resources/js/account/data-table.js",
  "resources/js/account/merge-field.js",
  "resources/js/account/merge-field-service.js",
  "resources/js/account/emoji-service.js",
  "resources/js/account/emoji-picker.js",
  "resources/js/account/notifications-menu.js",
  "resources/js/account/product-updates-service.js",
  "resources/js/account/project-nav.js",
  "resources/js/account/project-select-menu.js",
  "resources/js/account/portal-select-menu.js",
  "resources/js/account/project-service.js",
  "resources/js/account/project-steps.js",
  "resources/js/account/rich-text-editor.js",
  "resources/js/account/star-count.js",
  "resources/js/account/table-list.js",
  "resources/js/account/lists-service.js",
  "resources/js/account/thumb-transform.js",
  "resources/js/account/video-transform.js",
  "resources/js/account/overlay-crop.js",
  "resources/js/account/overlay-enterprise-pop-up.js",
  "resources/js/account/overlay-list-save.js",
  "resources/js/account/overlay-loading.js",
  "resources/js/account/overlay-password-change.js",
  "resources/js/account/overlay-project-create.js",
  "resources/js/account/overlay-increase-endowment-limit.js",
  "resources/js/account/overlay-video-delete.js",
  "resources/js/account/overlay-video-review.js",
  "resources/js/account/overlay-video-thumbnail.js",
  "resources/js/account/overlay-video-trim.js",
  "resources/js/account/home.js",
  "resources/js/account/user-terms-service.js",
  "resources/js/account/overlay-new-account.js",
  "resources/js/account/overlay-library-delete.js",
  "resources/js/account/overlay-request-rerecord.js",
  "resources/js/account/overlay-project-resend.js",
  "resources/js/account/contact-profile.js",
  "resources/js/account/givebutter-service.js",
  "resources/js/account/captions-settings-service.js",
  "resources/js/account/overlay-upgrade-flow.js",
  "resources/js/account/overlay-library-copy.js",
  "resources/js/account/overlay-view-test-sends.js",

  "resources/js/guests/guests.js",
  "resources/js/guests/guest-import.js",
  "resources/js/guests/guest-crm-import.js",
  "resources/js/guests/guest-nxt-import.js",
  "resources/js/guests/guest-list.js",
  "resources/js/guests/import-options-service.js",
  "resources/js/guests/task-service.js",
  "resources/js/guests/guest-manual.js",
  "resources/js/guests/guest-select.js",
  "resources/js/guests/overlay-guest-add.js",
  "resources/js/guests/overlay-guest-import-edit.js",
  "resources/js/guests/overlay-create-task.js",
  "resources/js/guests/guest-import-approve.js",
  "resources/js/guests/guests-table.js",
  "resources/js/guests/guest-duplicate-list.js",

  "resources/js/landing/landing.js",
  "resources/js/landing/overlay-live-preview.js",

  "resources/js/library/library.js",
  "resources/js/library/overlay-folder.js",
  "resources/js/library/library-layout.js",
  "resources/js/library/library-service.js",
  "resources/js/library/overlay-library-upload.js",
  "resources/js/library/overlay-edit-video.js",
  "resources/js/library/overlay-splice-preview.js",

  "resources/js/personal-video/personal-video.js",
  "resources/js/personal-video/overlay-video-library.js",

  "resources/js/lists/lists.js",
  "resources/js/lists/overlay-list.js",
  "resources/js/lists/overlay-duplicate-list.js",

  "resources/js/projects/projects.js",
  "resources/js/projects/overlay-project-copy.js",
  "resources/js/projects/overlay-project-edit.js",
  "resources/js/onboarding/overlay-onboarding.js",
  "resources/js/onboarding/onboarding-banner.js",

  "resources/js/recordings/recordings.js",
  "resources/js/recordings/recordings-accordion-directive.js",

  "resources/js/recordings/video/recordings-video.js",
  "resources/js/recordings/video/recordings-video-layout.js",
  "resources/js/recordings/video/recordings-video-list.js",
  "resources/js/recordings/video/recordings-video-recorder.js",
  "resources/js/recordings/video/overlay-library.js",

  "resources/js/recordings/intro/recordings-intro.js",
  "resources/js/recordings/intro/recordings-intro-form.js",
  "resources/js/recordings/intro/recordings-intro-layout.js",

  "resources/js/recordings/outro/recordings-outro.js",
  "resources/js/recordings/outro/recordings-outro-layout.js",
  "resources/js/recordings/outro/recordings-outro-form.js",

  "resources/js/recordings/overlay/overlay-tasks-rerecord.js",
  "resources/js/recordings/overlay/recordings-video-overlay.js",
  "resources/js/recordings/overlay/recordings-video-overlay-layout.js",

  "resources/js/recordings/templates/recordings-template-preview.js",
  "resources/js/recordings/templates/animations/chiyogami-photo.js",
  "resources/js/recordings/templates/animations/chiyogami-text.js",
  "resources/js/recordings/templates/animations/circle.js",
  "resources/js/recordings/templates/animations/class-act.js",
  "resources/js/recordings/templates/animations/default.js",
  "resources/js/recordings/templates/animations/emboss.js",
  "resources/js/recordings/templates/animations/light-leak.js",
  "resources/js/recordings/templates/animations/linen.js",
  "resources/js/recordings/templates/animations/polkadots.js",
  "resources/js/recordings/templates/animations/tryptic.js",

  "resources/js/captions/captions-service.js",
  "resources/js/captions/overlay-captions-edit.js",
  "resources/js/captions/overlay-captions-upload.js",
  "resources/js/captions/overlay-captions-settings.js",
  "resources/js/captions/overlay-captions-balance.js",
  "resources/js/captions/overlay-captions-get.js",

  "resources/js/replies/replies.js",

  "resources/js/sends/sends.js",
  "resources/js/sends/sends-layout.js",
  "resources/js/sends/overlay-send.js",
  "resources/js/sends/overlay-send-confirm.js",
  "resources/js/sends/overlay-send-sample.js",
  "resources/js/sends/overlay-send-url.js",
  "resources/js/sends/overlay-verify.js",

  "resources/js/settings/settings.js",
  "resources/js/settings/my-profile.js",
  "resources/js/settings/my-subscription.js",
  "resources/js/settings/my-portal.js",
  "resources/js/settings/security.js",
  "resources/js/settings/manage-users.js",
  "resources/js/settings/accessibility.js",
  "resources/js/settings/notifications.js",
  "resources/js/settings/integrations-apis.js",
  "resources/js/settings/search-dropdown.js",
  "resources/js/settings/settings-layout.js",
  "resources/js/settings/overlay-custom-merge.js",
  "resources/js/settings/overlay-user-edit.js",
  "resources/js/settings/overlay-user-password.js",
  "resources/js/settings/overlay-user-phone.js",
  "resources/js/settings/overlay-add-user.js",
  "resources/js/settings/overlay-login-evertrue.js",
  "resources/js/settings/overlay-login-givebutter.js",
  "resources/js/settings/overlay-sso-auth.js",

  "resources/js/setup/setup.js",
  "resources/js/setup/email-preview.js",
  "resources/js/setup/overlay-change-language.js",
  "resources/js/setup/overlay-follow-up.js",

  "resources/js/credits/credits.js",
];

/**
 * Metrics page dependencies
 */
const metricsScripts = [
  "resources/js/metrics/metrics-controller.js", // defines the metrics module
  "resources/js/global/plugins/html2canvas.js",
  "resources/js/metrics/metrics-chart.js",
  "resources/js/metrics/metrics-filters.js",
  "resources/js/metrics/metrics-fundraising-donors.js",
  "resources/js/metrics/metrics-recipients.js",
  "resources/js/metrics/metrics-service.js",
  "resources/js/metrics/metrics-maps-service.js",
  "resources/js/metrics/metrics-table.js",
  "resources/js/metrics/metrics-personal-video-users-table.js",
  "resources/js/metrics/metrics-pdf.js",
  "resources/js/metrics/overlay-recipients-info.js",
  "resources/js/metrics/overlay-metrics-tutorial.js",
];

const adminScripts = [
  'resources/js/admin/admin.js',
  'resources/js/admin/adminUtils.js',
  'resources/js/admin/admin-layout.js',
  'resources/js/admin/admin-dropdown.js',
  'resources/js/admin/admin-business-forms.js',
  'resources/js/admin/admin-design-forms.js',
  'resources/js/admin/admin-reports-forms.js',
  'resources/js/admin/admin-dns-forms.js',
  'resources/js/admin/admin-sales-forms.js',
  'resources/js/admin/admin-features-forms.js',
  'resources/js/admin/admin-emails-forms.js',
  'resources/js/admin/admin-odder-forms.js',
  'resources/js/admin/admin-sso-forms.js',
  'resources/js/admin/admin-users-forms.js',
  'resources/js/admin/overlay-client-list.js',
  'resources/js/admin/package-service.js',
  'resources/js/admin/admin-welcome-forms.js',
  'resources/js/admin/admin-commission.js',
  'resources/js/admin/admin-evertrue-forms.js',
  'resources/js/admin/admin-salesforce-forms.js',
  'resources/js/admin/admin-blackbaud-forms.js',

  "resources/js/admin/admin-feature-usage/admin-feature-usage.js",

  "resources/js/quickbooks/quickbooks.js",
  "resources/js/quickbooks/quickbooks-service.js",
];

mix.setPublicPath(path.normalize("public/build/"));

if (process.env.NODE_ENV === "development") {
  console.log("-----------------------------");
  console.log(`DEVELOPMENT BUILD v ${packageJson.version}`);
  console.log("-----------------------------");
  mix
    .scripts(runtimeScripts, "public/build/js/plugins.js")
    .js(preLoginScripts, "public/build/js/app.js")
    .js(postLoginScripts, "public/build/js/account.js")
    .js(metricsScripts, "public/build/js/metrics.js")
    .js(adminScripts, "public/build/js/admin.js")
    .vue()
    .react()
    .sourceMaps();
  mix.webpackConfig({
    devtool: "source-map",
  });
} else {
  console.log("----------------------------");
  console.log(`PRODUCTION BUILD v ${packageJson.version}`);
  console.log("----------------------------");
  // mix.options({
  //     terser: {
  //         terserOptions: {
  //             compress: {
  //                 drop_console: true,
  //             },
  //         },
  //     },
  // });
  mix
    .scripts(runtimeScripts, "public/build/js/plugins.js")
    .js(preLoginScripts, "public/build/js/app.js")
    .js(postLoginScripts, "public/build/js/account.js")
    .js(metricsScripts, "public/build/js/metrics.js")
    .js(adminScripts, "public/build/js/admin.js")
    .vue()
    .react()
    .polyfill({
      enabled: true,
      useBuiltIns: "usage",
      targets: "IE 11", // TODO: only transpile video landing page scripts to IE 11
    })
    .sourceMaps();
  mix.webpackConfig({
    devtool: "source-map",
  });
}

mix
  .sass("resources/sass/account.scss", "css")
  .sass("resources/sass/admin/admin.scss", "css")
  .sass("resources/sass/global.scss", "css")
  .sass("resources/sass/prelogin.scss", "css")
  .sass("resources/sass/video.scss", "css")
  .sass("resources/sass/video-request.scss", "css")
  .sass("resources/sass/embed-recorder.scss", "css");

mix.webpackConfig({
  module: {
    rules: [
      {
        test: /\.(scss|sass)$/,
        enforce: 'pre',
        use: [
          {
            loader: 'sass-loader',
            options: {
              prependData: `$asset-url: "${process.env.AWS_S3_ASSET_URL || 'https://assets.thankview.com'}";`
            }
          }
        ]
      }
    ]
  },
  plugins: [
    new webpack.DefinePlugin({
      "process.env.AWS_S3_ASSET_URL": JSON.stringify(
        process.env.AWS_S3_ASSET_URL || "https://assets.thankview.com"
      ),
    }),
  ],
  output: { publicPath: "build/" },
});

if (process.env.NODE_ENV !== "development") {
  mix
    .purgeCss({
      enabled: true,
      content: [
        "public/templates/**/*.html",
        "resources/js/**/*.js",
        "resources/js/**/*.jsx",
        "resources/js/**/*.vue",
        "resources/views/**/*.blade.php",
        "node_modules/@evertrue/tv-components/**/*.vue",
      ],
      safelist: [
        "how-it-works",
        "questions",
        "purple",
        "js-focus-visible",
        "mobile-recording",
        "state-waiting",
        "is-open",
        /\S+flex-67/,
        /jw\S+/,
        /video-js/,
        /vjs\S+/,
        /ng\S+/,
        /business-slug-\S+/,
        /project-type-icon--\S+/,
        /\S+-text/,
        /trix-\S+/,
        /hs\S+/,
        /chilipiper\S+/,
        /hover:\S+/,
        /:hover/,
        /section-headers\S+/,
        /project-type-\S+/,
        /btn--\S+/,
        /data-\S+/,
      ],
    })
    .version(); // see version hash in mix-manifest.json
} else {
  console.log("in development: not versioning");
}

mix.options({
  hmrOptions: {
    host: "dev-thankview.com",
    port: 443,
  },
});

mix.webpackConfig({
  output: {
    publicPath: "build/",
  },
});
