@extends('app', ['namespace' => 'thankview.login'])

@section('title', 'ThankView: Log in')

@section('scripts')
	<script>
	(function(){
		function initIntercom() {
			if (typeof config === 'undefined' || !config.intercomAppId) {
				// Wait for config to be available
				setTimeout(initIntercom, 100);
				return;
			}
			window.intercomSettings = {app_id: config.intercomAppId, hide_default_launcher: true};
			var w=window;var ic=w.Intercom;
			if(typeof ic==="function"){
				ic('reattach_activator');
				ic('update',intercomSettings);
				ic('hide'); // Hide the launcher
			}else{
				var d=document;var i=function(){i.c(arguments)};i.q=[];i.c=function(args){i.q.push(args)};w.Intercom=i;
				function l(){
					var s=d.createElement('script');s.type='text/javascript';s.async=true;
					s.src='https://widget.intercom.io/widget/' + config.intercomAppId;
					var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);
				}
				if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}
			}
		}
		initIntercom();
	})()
	</script>
@endsection

@section('styles')
	<style>
		.login-cta {
			color: {{ $content['login_cta_text_color'] }};
			background-color: {{ $content['login_cta_background_color'] ?? 'transparent' }};
			border-color: {{ $content['login_cta_border_color'] ?? '#fff' }};
		}
	</style>
	<link rel="stylesheet" href="{{ mix('css/prelogin.css', 'build') }}">
@endsection

@section('content')
	<div class="login flex pad-t-0px" ng-class="{fadeIn:loginLoaded}" ng-controller="LoginController" ng-cloak>
		<div class="side-bar flex flex-wrap align-items-end justify-content-center bg-white">
			<div class="contents-sign-in margin-t-0px margin-r-0px margin-b-0px margin-l-0px width-90 text-align-center" >
				@if(isset($businessLogo))
					<div class="business-logo margin-center">
						@if(env("APP_ENV") == "production")
							<img class="max-width-200px max-height-200px" src="{{ env('AWS_S3_ASSET_URL') }}/assets/img/logos/{{ $businessLogo }}" alt="Business logo" />
						@else
							<img class="max-width-200px max-height-200px" src="{{ env('AWS_S3_ASSET_URL') }}/assets/img/logos/{{ $businessLogo }}" alt="Business logo" />
						@endif
					</div>
				@else
					<div class="business-logo margin-center max-width-300px max-height-100px">
						<img class="width-100" src="{{ env('AWS_S3_ASSET_URL') }}/assets/img/tv-logo-2019-navy.svg" />
					</div>
				@endif

				<form name="form" class="auth-form" novalidate role="form" method="POST" action="{{ request()->is('login_ca') ? '/login_ca' : '/login' }}" ng-submit="checkValid($event)" tv-auth-form fb-auth>
					<input type="hidden" name="_token" value="{{ csrf_token() }}">

					@if (count($errors) > 0)
						<div class="alert alert-danger font-size-14px text-align-left">
							<ul>
								@foreach ($errors->all() as $error)
									<li>{!! $error !!}</li>
								@endforeach
							</ul>
						</div>
					@endif

					<div class="input-field">
						<input class="tv-text-input" type="email" name="email" placeholder="Email" data-value="{{ old('email') }}" maxlength="100" ng-model="user.email" required>
						<div ng-show="!form.$pristine && form.email.$touched || form.$submitted" class="validation-messages" ng-cloak>
							<div ng-show="form.email.$error.required"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> Email is required.</div>
							<div ng-show="form.email.$error.email"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> This is not a valid email.</div>
					    </div>
					</div>
					<div class="input-field">
						<input class="tv-text-input" type="password" autocomplete="off" name="password" placeholder="Password" ng-model="user.password" maxlength="50" required>
						<div ng-show="!form.$pristine && form.password.$touched || form.$submitted"  class="validation-messages" ng-cloak>
							<div ng-show="form.password.$error.required"><i class="fa fa-exclamation-circle" aria-hidden="true"></i> Please enter a password.</div>
					    </div>
					</div>

					<div class="font-size-16px text-align-left margin-b-16px">
						<label class="tv-checkbox">
							<input class="tv-checkbox__input" type="checkbox" name="remember" ng-model="user.remember">
							<div class="tv-checkbox__visual">
								<i class="far fa-square"></i>
								<i class="fas fa-check-square"></i>
							</div>
							<span class="tv-checkbox__text">Remember Me</span>
						</label>
					</div>

					<button type="submit" class="btn btn--primary">Sign In</button>

					@if (isset($sso_config) && !$sso_config->isEmpty())
						<hr/>
						@foreach($sso_config as $sso)
							<a href="/auth/{{$sso['slug']}}" class="btn btn--{{$sso['slug']}}"><img src="{{$sso['icon_btn']}}"/>Sign in with {{$sso['display_name']}}</a>
						@endforeach
					@endif

					<div class="links text-align-center">
						<div>
							<a href="/password/reset">Forgot your password?</a>
						</div>
						<div ng-hide="hasSSO">
                        <magic-link logo='logo'></magic-link>
						</div>
					</div>
				</form>

			</div>
			<div class="footer-signin text-align-center font-size-16px">
				<p class="margin-t-auto pad-t-0px pad-r-0px pad-l-0px pad-b-0px">&copy;2015-{{ $year }} Thankview, LLC.</p>
				<ul class="font-blue-48">
					<li class="inline-block pad-r-16px"><a ng-href="https://www.evertrue.com/privacy-policy/">Privacy</a></li>
					<li class="inline-block pad-l-16px pad-r-16px"><a href="https://www.evertrue.com/terms-and-conditions/">Terms</a></li>
					<li class="inline-block pad-l-16px"><a href="https://status.evertrue.com/">Status</a></li>
				</ul>
			</div>
		</div>
		<div class="contents-updates text-align-center bg-size-cover bg-repeat-no none lg-block" ng-click="trackPromoImage()" style="background-image: url({{ $content['bkgd_image'] }})">
			<div>
				<h1 class="margin-b-0px margin-center font-size-36px" style="color:{{$content['login_header_text_color']}}">{{ $content['login_header_text'] }}</h1>
				<p class="margin-t-8px font-size-20px margin-b-32px" style="color:{{$content['login_message_text_color']}}">{{ $content['login_message_text'] }}</p>
				@if(isset($content['login_cta_text']) && isset($content['login_cta_url']))
					<a class="login-cta btn btn--large" href="{{ $content['login_cta_url'] }}" target="_blank" ng-click="trackLoginButton(); $event.stopPropagation();">{{ $content['login_cta_text'] }}</a>
				@endif
			</div>
		</div>
		<overlay-paywall></overlay-paywall>
	</div>
@endsection