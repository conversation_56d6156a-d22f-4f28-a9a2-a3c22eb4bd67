(function () {
    'use strict';

    angular.module('thankview.metrics')
        .factory('MetricsService', ['$timeout', '$q', 'ajax', function ($timeout, $q, ajax) {
            var metrics;
            var ajaxCancelor;

            function getUserTotalOfType(metricType, user, dates) {
                return user.stats.reduce(function (previousTotal, currentStat) {
                    var filter = false;

                    if (dates) {
                        var statDate = moment.utc(currentStat.date, 'YYYY-MM-DD HH:mm:ss');
                        if (statDate.diff(dates[0], 'hours') <= 0 || statDate.diff(dates[1], 'hours') > 0) {
                            filter = true;
                        }
                    }

                    return previousTotal + (!filter ? parseFloat(currentStat[metricType], 10) : 0);
                }, 0);
            }

            function getTotalOfType(metricType, data, dates) {
                return data.reduce(function (previousTotal, currentUser) {
                    return previousTotal + getUserTotalOfType(metricType, currentUser, dates);
                }, 0);
            }

            function getTableData(data) {
                return data.map(function (user) {
                    return {
                        name: user.name,
                        sent: getUserTotalOfType('sent', user),
                        opens: getUserTotalOfType('opens', user),
                        viewed: getUserTotalOfType('read_email', user),
                        started: getUserTotalOfType('started', user),
                        finished: getUserTotalOfType('video_pos_3s', user),
                        clicked: getUserTotalOfType('cta_clicked', user),
                    };
                });
            }


            function filterMetrics() {
                var data = angular.copy(metrics);
                var users = {};

                data.forEach(function (row) {
                    var key = JSON.stringify(row.name);
                    users[key] = users[key] || { name: row.name, stats: [] };
                    users[key].stats.push(row);
                });

                data = [];
                angular.forEach(users, function (user) {
                    data.push(user);
                });

                var result = {
                    totalSent: getTotalOfType('sent', data),
                    totalDelivered: getTotalOfType('delivered', data),
                    totalOpened: getTotalOfType('opens', data),
                    totalViewed: getTotalOfType('read_email', data),
                    totalStarted: getTotalOfType('started', data),
                    totalPos1: getTotalOfType('video_pos_1s', data),
                    totalPos2: getTotalOfType('video_pos_2s', data),
                    totalPos3: getTotalOfType('video_pos_3s', data),
                    totalCompleted: getTotalOfType('completed', data),
                    totalClicked: getTotalOfType('cta_clicked', data),
                    totalViews: getTotalOfType('views', data),
                    totalDownloads: getTotalOfType('downloads', data),
                    totalShares: getTotalOfType('shares', data),
                    totalUnsubscribes: getTotalOfType('unsubscribes', data),
                    totalBounced: getTotalOfType('bounced', data),
                    uniquePDFViews: getTotalOfType('pdf_unique_views', data),
                    totalPDFViews: getTotalOfType('pdf_total_page_views', data),
                    table: getTableData(data),
                    percDelivered: 0,
                    percOpened: 0,
                    percViewed: 0,
                    percStarted: 0,
                    percPos3: 0,
                    percUnsubscribes: 0,
                    percBounced: 0,
                    watchedPerc: 0,
                    avgPageViews: 0,
                    averagePDFCompleted: 0,
                    avgPageNumPDF: 0,
                    deletedContactsSpam: data?.deletedContactsSpam || 0,
                    deletedContactsBounced: data?.deletedContactsBounced || 0,
                };

                if (result.totalSent > 0) {
                    result.percDelivered = Math.round((result.totalDelivered / result.totalSent) * 1000) / 10;
                    result.percBounced = Math.round((result.totalBounced / result.totalSent) * 1000) / 10;
                    result.percUnsubscribes = Math.round((result.totalUnsubscribes / result.totalSent) * 1000) / 10;
                }
                if (result.totalStarted > 0) {
                    var numerator = result.totalStarted + result.totalPos1 * 2 + result.totalPos2 * 2 + result.totalPos3 * 2 + result.totalCompleted;
                    result.watchedPerc = Math.round(numerator / (8 * result.totalStarted) * 1000) / 10;
                }
                if (result.totalDelivered > 0) {
                    result.percOpened = Math.round((result.totalOpened / result.totalDelivered) * 1000) / 10;
                    result.percViewed = Math.round((result.totalViewed / result.totalDelivered) * 1000) / 10;
                    result.percStarted = Math.round((result.totalStarted / result.totalDelivered) * 1000) / 10;
                    result.percPos3 = Math.round((result.totalPos3 / result.totalDelivered) * 1000) / 10;
                }
                if (result.totalPDFViews && result.uniquePDFViews > 0) {
                    result.avgPageViews = Math.min(parseFloat(result.totalPDFViews / result.uniquePDFViews), result.avgPageNumPDF);
                }
                if (result.avgPageNumPDF > 0 && result.avgPageViews > 0) {
                    result.averagePDFCompleted = Math.round((result.avgPageViews / result.avgPageNumPDF) * 1000) / 10;
                }

                return result;
            }

            function getMetrics(params) {
                if (ajaxCancelor) {
                    ajaxCancelor.resolve();
                }
                var deferred = $q.defer();
                ajaxCancelor = $q.defer();

                ajaxCancelor = $q.defer();
                var fetchMetrics = [
                    ajax.get(
                        '/api/metrics',
                        params,
                        { cancelor: ajaxCancelor, silent: true },
                    ),
                    ajax.get(
                        '/api/metrics/reports',
                        params,
                        { cancelor: ajaxCancelor, silent: true },
                    ),
                ];
                $q.all(fetchMetrics).then(function (data) {
                    var result;

                    if (!data[0]) {
                        return;
                    }
                    metrics = data[0];

                    if (data[1]) {
                        metrics.forEach(function (row) {
                            const reportKeyVals = Object.entries(data[1][0] || {});
                            reportKeyVals.forEach(([key, val]) => { row[key] = val; });
                        });
                    }

                    result = filterMetrics();

                    deferred.resolve(result);
                }).catch(function () {
                    deferred.reject();
                });

                return deferred.promise;
            }

            return {
                getMetrics: getMetrics,
                filterMetrics: filterMetrics,
            };
        }]);
}());
