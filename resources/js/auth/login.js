(function () {
    'use strict';

    angular.module('thankview.login', ['ngTouch', 'thankview.global', 'thankview.auth', 'thankview.pricing'])
        .controller('LoginController', ['$scope', '$rootScope', 'pricingService', '$window', 'dates', 'ajax', '$timeout', '$q', 'logEventService', 'intercomService', function ($scope, $rootScope, pricingService, $window, dates, ajax, $timeout, $q, logEventService, intercomService) {
            $rootScope.bodyClass = 'body-login';
            const deferred = $q.defer();
            $scope.isLogin = true;


            // events
            $scope.$on('load-intercom', function () {
                $scope.loadIntercom();
            });

            var getBusiness = function ($bizSlug) {
                ajax.get('/api/business/slug/' + $bizSlug).then(function (data) {
                    $scope.business = data.business;
                });
            };

            var getBusinessLogo = function ($slug) {
                ajax.get('/api/business/logo/' + $slug).then(function (data) {
                    const businessLogo = data.logo;
                    $scope.logo = businessLogo ? config.cdnPath + '/img/logos/' + businessLogo : null;
                });
            };

            $scope.getBusinessUuid = (slug) => {
                ajax.get(`/api/business/${slug}/uuid`).then(function (data) {
                    $scope.businessUuid = data.uuid;
                    deferred.resolve($scope.businessUuid);
                });
                return deferred.promise;
            };

            var requestAccess = function () {
                var message;
                var params = {};

                params.dontClose = true;
                params.buttons = [
                    { name: 'Go Back', colorClass: 'btn--secondary-alt', callback: function () { $rootScope.$broadcast('force-close-overlay'); } },
                    { name: 'Request Access', colorClass: 'btn--primary', callback: function () { $scope.requestAccessConfirm(); } },
                ];
                params.header = 'Unable to Access ThankView';
                message = 'To access ThankView, an admin of ' + $scope.business.full_name + ' needs to add you as a user. Please contact your administrator to get access.';
                $rootScope.$broadcast('open-message-confirm', message, null, params);
            };

            /**
             * Call services after page load is completed
             */
            $scope.ready = function () {
                // Initialize Intercom after a longer delay to avoid form conflicts
                $timeout(function () {
                    $scope.loadIntercom();
                }, 2000);
            };

            /**
             * Load intercom service
             */
            $scope.loadIntercom = function () {
                // Initialize Intercom with launcher hidden
                intercomService.init({
                    hide_default_launcher: true
                });

                // Ensure launcher is hidden after initialization
                $timeout(function () {
                    if (typeof Intercom !== 'undefined' && typeof config !== 'undefined' && config.intercomAppId) {
                        // Hide the launcher
                        Intercom('hide');

                        // Alternative: Reboot with launcher hidden
                        Intercom('boot', {
                            app_id: config.intercomAppId,
                            hide_default_launcher: true
                        });
                    } else {
                        console.warn('Config or Intercom not available yet');
                    }
                }, 1000);

                // track page visit
                $window.requestIdleCallback($scope.intercomPageTrack, {
                    timeout: 5000,
                });
            };

            /**
             * Track intercom event
             */
            $scope.intercomPageTrack = function () {
                var pathParts = $window.location.pathname.split('/');
                var page = pathParts[pathParts.length - 1];

                intercomService.trackEvent('visited-' + page + '-page');
            };

            $scope.init = function () {
                // fetch a specific payment, or just check for due payments
                if ($window.location.search.indexOf('payment') > -1) {
                    const paymentId = $window.location.search.split('=')[1];
                    pricingService.fetchPayment(paymentId).then((response) => {
                        if (response.payment) {
                            $rootScope.$broadcast('open-paywall', { payment: response.payment, dontClose: true });
                        } else if (response.paid) {
                            const message = 'Thank you for your payment!';
                            $rootScope.$broadcast('open-message', message);
                        }
                    }).catch((error) => {
                        console.log(error);
                    });
                } else if ($window.location.search.indexOf('request') > -1) {
                    getBusiness($window.location.host.split('.')[0]);
                    $scope.requestEmail = $window.location.search.split('=')[1];
                    $timeout(function () { requestAccess(); }, 500);
                } else if ($scope.business && $window.location.search !== '?admin') {
                    $scope.fetchPayments();
                }
                // Check if config.params is defined
                if (config.params !== undefined && config.params !== null) {
                    // Check if config.params.sso_config is defined
                    if (config.params.sso_config !== undefined) {
                        // Check if config.params.sso_config has a length greater than 0
                        $scope.hasSSO = config.params.sso_config.length > 0;
                    } else {
                        // If config.params.sso_config is not defined, set $scope.hasSSO to false
                        $scope.hasSSO = false;
                    }
                } else {
                    // If config.params is not defined, set $scope.hasSSO to false
                    $scope.hasSSO = false;
                }


                getBusinessLogo($window.location.host.split('.')[0]);
            };

            $scope.fetchPayments = function () {
                pricingService.fetchBusiness($scope.business).then(function (response) {
                    var message;
                    if (response.suspended) {
                        message = 'Your portal has been temporarily suspended because of ' + response.suspended_reason + '. Please contact your success manager or our support email to learn how to avoid future issues and reactivate your account.';
                        $rootScope.$broadcast('open-message', message, { dontClose: true, showContact: true, header: 'Temporary Suspension' });
                        return;
                    }

                    // Open extend free trial pop-up if applicable
                    if (response.freeTrialExpired) {
                        $scope.getBusinessUuid($scope.business).then(() => {
                            message = 'You can extend your trial by 16 days or get started with a paid subscription immediately!';
                            const params = {
                                header: 'Your Free Trial Has Ended',
                                buttons: [
                                    { name: 'Cancel', colorClass: 'btn--cancel', callback: function () { $rootScope.$broadcast('force-close-overlay'); } },
                                    { name: 'Contact Sales', callback: function () { $scope.contactSales(); } },
                                ],
                                dontClose: true,
                            };
                            $rootScope.$broadcast('open-message', message, params);
                        });
                    }

                    // show error if there's no pending payment
                    if (response.isPaywallActive && !response.freeTrialExpired) {
                        console.warn(response);
                        var pack = response.package;

                        if (!pack) {
                            message = 'Your portal has expired. Please contact our support team to start a new subscription.';
                            $rootScope.$broadcast('open-message', message, { dontClose: true, showContact: true, header: 'Portal Expired' });
                            return;
                        }

                        var startDate = dates.convert(pack.subscription_start_date);
                        var now = new Date();

                        if (startDate && startDate > now && pack.details.payments_paid > 0) {
                            message = 'Your portal\'s subscription will begin on ' + dayjs(startDate).format('M/DD/YYYY') + '. If this is a mistake, please contact our support team.';
                            $rootScope.$broadcast('open-message', message, { dontClose: true, showContact: true, header: 'Temporary Suspension' });
                            return;
                        }

                        $rootScope.$broadcast('open-paywall', { payment: response.payment, dontClose: true });
                    }
                });
            };

            $scope.checkValid = function ($event) {
                if (!config.cookieEnabled) {
                    $rootScope.$broadcast('open-message', 'Your cookies are disabled. Please enable cookies to sign up.');
                    $event.preventDefault();
                }

                if (!$scope.form.$valid) {
                    $scope.form.$setSubmitted();
                    $event.preventDefault();
                }

                if (config.params.sso_config.length && !$scope.user.email.includes('@thankview.com') && !$scope.user.email.includes('@evertrue.com')) {
                    $event.preventDefault();
                    var message;
                    var params = {};

                    params.dontClose = true;
                    params.buttons = [
                        { name: 'Go Back', colorClass: 'btn--secondary-alt', callback: function () { $rootScope.$broadcast('force-close-overlay'); } },
                    ];
                    config.params.sso_config.forEach(function (sso) {
                        var button = {
                            name: 'Sign in with ' + sso.display_name, colorClass: 'btn--' + sso.slug, href: '/auth/' + sso.slug,
                        };
                        params.buttons.push(button);
                    });
                    params.header = 'Single Sign-On Enabled';
                    message = 'Your portal has enabled single sign-on for authentication.';
                    $rootScope.$broadcast('open-message-confirm', message, null, params);
                }
            };

            $scope.requestAccessConfirm = function () {
                var params = {
                    email: $scope.requestEmail,
                    slug: $scope.business.slug,
                };
                ajax.post('/api/sso/request', params).then(function (data) {
                    if (data && !data.success) {
                        $scope.loading = false;
                        $scope.incorrectLogin = true;
                    } else {
                        $scope.loading = false;
                        $scope.incorrectLogin = false;
                        $scope.$broadcast('force-close-overlay');

                        var message = $scope.currentSso.display_name + 'Access requested!';
                        $rootScope.$broadcast('open-message', message);
                    }
                }).catch(function () {
                    $scope.loading = false;
                    $scope.incorrectLogin = true;
                });
            };

            $scope.copyUrl = function (url, inputName) {
                $rootScope.copyToClipboard(url, () => {
                    $scope[inputName + '_copied'] = true;
                    $timeout(() => {
                        $scope[inputName + '_copied'] = false;
                    }, 3500);
                });
            };

            $scope.contactSales = function () {
                $scope.$broadcast('force-close-overlay');
                $rootScope.$broadcast('open-loading');
                const params = {
                    header: 'Our Team will Reach Out Shortly!',
                };
                ajax.post('/api/business/' + $window.location.host.split('.')[0] + '/freeTrialPurchase/email').then(function () {
                    $rootScope.$broadcast('close-loading');
                    $rootScope.$broadcast('open-message', 'Thanks for reaching out to Sales about purchasing a plan with EverTrue! Our team will contact you shortly to assist you.', params);
                });
            };

            $scope.trackLoginButton = () => {
                logEventService.addEvent('Login Page - Promo clicked', 'Function');
            };

            $scope.trackPromoImage = () => {
                logEventService.addEvent('Login Page - Promo Image clicked', 'Function');
            };


            $scope.init();
        }]);
}());
