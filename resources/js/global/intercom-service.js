(function () {
    'use strict';

    angular.module('thankview.global')
        .factory('intercomService', ['$timeout', '$document', '$window', function ($timeout, $document, $window) {
            const intercomService = {};
            const customLauncherSelector = '.tv-intercom-launcher';

            /**
             * Initialize intercom. Allow initial config to be overwritten
             * @param {Object} additionalConfig - additional configuration to initialize
             */
            intercomService.init = function (additionalConfig) {
                // Check if config is available
                if (typeof config === 'undefined' || !config.intercomAppId) {
                    console.warn('Config not available yet, retrying in 100ms...');
                    $timeout(function () {
                        intercomService.init(additionalConfig);
                    }, 100);
                    return;
                }

                // Create initConfig when init is called, not when service is defined
                let initConfig = {
                    app_id: config.intercomAppId,
                };

                // Only add user details if user is authenticated
                if (config.user && config.user.id) {
                    initConfig.name = config.user.name;
                    initConfig.email = config.user.email;
                    initConfig.user_id = config.user.id;
                    initConfig.user_hash = config.user_hash;
                }
                if (!$window.Intercom) {
                    $timeout(function () {
                        intercomService.boot(additionalConfig);
                    }, 1000);
                    return;
                }

                if (additionalConfig) {
                    initConfig = angular.extend(initConfig, additionalConfig);
                }

                // detect if there's a custom launch button on the page
                if ($document[0].querySelectorAll(customLauncherSelector).length > 0) {
                    initConfig.custom_launcher_selector = customLauncherSelector;
                    initConfig.hide_default_launcher = true;
                    initConfig.vertical_padding = 80;
                }

                Intercom('boot', initConfig);

                if ($window.location.search.indexOf('showMessenger=1') !== -1) {
                    intercomService.open();
                }
            };

            /**
             * Set the position relative to the bottom of the screen.
             * @param {Integer} bottom - number of pixels from bottom of screen
             * @return {Boolean} - whether intercom icon was successfully positioned
             */
            intercomService.positionBot = function (bottom) {
                var frames = $document[0].querySelectorAll('#intercom-container > div > iframe');

                for (var i = 0; i < frames.length; i++) {
                    frames[i].setAttribute('style', 'bottom: ' + bottom + 'px !important; width: 60px !important;');
                }

                return !!frames.length;
            };

            /**
             * Track an event in intercom
             * @param {String} event - name of the event
             * @param {Object} metadata - added metadata for event
             */
            intercomService.trackEvent = function (event, metadata) {
                Intercom('trackEvent', event, metadata);
            };

            /**
             * Open the intercom chat window
             */
            intercomService.open = function () {
                Intercom('show');
            };

            /**
             * Bind when the number of unread messages has changed
             * @param {Function} callback - function to execute
             */
            intercomService.onUnreadCountChange = function (callback) {
                Intercom('onUnreadCountChange', callback);
            };

            /**
             * Close the intercom chat window
             */
            intercomService.close = function () {
                Intercom('shutdown');
            };

            return intercomService;
        }]);
}());
